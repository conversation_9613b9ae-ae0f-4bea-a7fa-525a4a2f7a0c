import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/features/stock/presentation/bloc/eod_bloc/eod_bloc.dart';
import 'package:stock_market/features/stock/presentation/bloc/eod_bloc/eod_bloc_state.dart';
import '../../bloc/stock_prices/stock_prices_bloc.dart';
import '../../bloc/stock_prices/stock_prices_event.dart';
import '../../bloc/stock_prices/stock_prices_state.dart';
import '../widgets/stock_price_chart.dart';

class StockDetailsPage extends StatelessWidget {
  final String symbol;
  final String name;

  const StockDetailsPage({
    Key? key,
    required this.symbol,
    required this.name,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<StockPricesBloc>();

    return Scaffold(
      appBar: AppBar(
        title: Text('$name ($symbol)'),
        centerTitle: true,
        elevation: 2,
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Bloc<PERSON><PERSON>er<EodBloc, EodState>(
          builder: (context, state) {
            // في الحالة الابتدائية، نرسل حدث التحميل مرة واحدة
            if (state is EodInitial) {
              bloc.add(eodev(symbol: symbol));
              return const Center(child: CircularProgressIndicator());
            }
            if (state is StockPricesLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is StockPricesLoaded) {
              final prices = state.prices;
              if (prices.isEmpty) {
                return const Center(child: Text('لا توجد بيانات سعرية.'));
              }
              return Column(
                children: [
                  // الرسم البياني
                  Expanded(
                    flex: 2,
                    child: StockPriceChart(prices: prices),
                  ),
                  const SizedBox(height: 16),
                  // تفاصيل كل يوم
                  Expanded(
                    flex: 1,
                    child: ListView.builder(
                      itemCount: prices.length,
                      itemBuilder: (context, index) {
                        final price = prices[index];
                        final date = price.date;
                        final dateStr =
                            '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: Text(dateStr,
                                style: const TextStyle(fontSize: 14)),
                            title: Text(
                                'الإغلاق: ${price.close.toStringAsFixed(2)}'),
                            subtitle: Text(
                              'الافتتاح: ${price.open.toStringAsFixed(2)}  |  '
                              'الأعلى: ${price.high.toStringAsFixed(2)}  |  '
                              'الأدنى: ${price.low.toStringAsFixed(2)}',
                            ),
                            trailing: Text(
                                'الحجم: ${price.volume.toStringAsFixed(0)}'),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
            } else if (state is EodError) {
              return Center(child: Text(state.message));
            }
            // أي حالة غير متوقعة
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
