
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/di/dependency_injection.dart';
import 'features/stock/presentation/bloc/popular_bloc/popular_event.dart';
import 'features/stock/presentation/bloc/theme_cubit/theme_cubit.dart';
import 'features/stock/presentation/bloc/popular_bloc/popular_bloc.dart';
import 'features/stock/presentation/bloc/search_bloc/search_bloc.dart';
import 'features/stock/presentation/bloc/eod_bloc/eod_bloc.dart';
import 'features/stock/presentation/screens/home_page.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await init();
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});



  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  ThemeMode _themeMode = ThemeMode.light;

  void toggleTheme() {
    setState(() {
      _themeMode = (_themeMode == ThemeMode.light) ? ThemeMode.dark : ThemeMode.light;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ThemeCubit>(
          create: (_) => sl<ThemeCubit>(),
        ),
        BlocProvider<PopularBloc>(
          create: (_) => sl<PopularBloc>()..add(FetchPopularStocksEvent()),
        ),
        BlocProvider<SearchBloc>(
          create: (_) => sl<SearchBloc>(),
        ),
        BlocProvider<EodBloc>(
          create: (_) => sl<EodBloc>(),
        ),
      ],
      child: BlocBuilder<ThemeCubit, ThemeMode>(
        builder: (context, themeMode) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            title: 'Stock Market Tracker',
            theme: ThemeData.light(),
            darkTheme: ThemeData.dark(),
            themeMode: themeMode,
            home: HomePage(toggleTheme: toggleTheme),
          );
        },
      ),
    );
  }
}
