import 'package:dio/dio.dart';
import 'package:stock_market/core/uitle/constants.dart';
import '../../../../core/error/exceptions.dart';
import '../models/stock_model.dart';
import '../models/eod_data_model.dart';

abstract class StockRemoteDataSource {
  Future<List<StockModel>> fetchPopularStocks();
  Future<List<StockModel>> searchStock(String query);
  Future<List<EodDataModel>> fetchEodData(String symbol);
}

class StockRemoteDataSourceImpl implements StockRemoteDataSource {
  final Dio dio;
  StockRemoteDataSourceImpl({required this.dio});
  @override
  Future<List<StockModel>> fetchPopularStocks() async {
    try {
      final response = await dio.get(
        '${Constants.baseUrl}/tickers?',
        queryParameters: {
          'access_key': Constants.apiKey,
          'limit': 10,
          //  'sort': 'changes_percentage',
          //  'sort_dir': 'desc',
        },
      );
      if (response.statusCode == 200) {
        final data = response.data['data'] as List;
        return data
            .map((json) => StockModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException();
      }
    } on DioError {
      throw ServerException();
    }
  }

  @override
  Future<List<StockModel>> searchStock(String query) async {
    try {
      final response = await dio.get(
        '${Constants.baseUrl}/tickers?',
        queryParameters: {
          'access_key': Constants.apiKey,
          'limit': 1,
          'search': query,
        },
      );
      if (response.statusCode == 200) {
        final data = response.data['data'] as List;
        return data.map((json) => StockModel.fromJson(json)).toList();
      } else {
        throw ServerException();
      }
    } on DioError {
      throw ServerException();
    }
  }

  @override
  Future<List<EodDataModel>> fetchEodData(String symbol) async {
    try {
      final response = await dio.get(
        '${Constants.baseUrl}/eod?',
        queryParameters: {
          'access_key': Constants.apiKey,
          'symbols': symbol,
          'limit': 5,
        },
      );
      if (response.statusCode == 200) {
        final data = response.data['data'] as List;
        return data
            .map((json) => EodDataModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException();
      }
    } on DioError {
      throw ServerException();
    }
  }
}
