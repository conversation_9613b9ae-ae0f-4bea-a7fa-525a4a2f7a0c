import 'package:equatable/equatable.dart';

abstract class TickerListEvent extends Equatable {
  const TickerListEvent();

  @override
  List<Object> get props => [];
}

/// الحدث لبدء تحميل قائمة الأسهم أول مرة
class LoadTickersEvent extends TickerListEvent {}

/// الحدث عند تنفيذ بحث: يحمل نص البحث
class SearchTickersEvent extends TickerListEvent {
  final String query;

  const SearchTickersEvent({required this.query});

  @override
  List<Object> get props => [query];
}
