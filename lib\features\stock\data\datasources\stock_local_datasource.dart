import 'dart:convert';

import 'package:dartz/dartz.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/error/exceptions.dart';
import '../models/stock_model.dart';
import '../models/eod_data_model.dart';


const CACHED_STOCKS = 'CACHED_STOCKS';
const CACHED_EOD_PREFIX = 'CACHED_EOD_';


abstract class StockLocalDataSource {

  Future<List<StockModel>> getCachedPopularStocks();
  Future<Unit> cachePopularStocks(List<StockModel> stocks);
  Future<List<EodDataModel>> getCachedEodData(String symbol);
  Future<Unit> cacheEodData(String symbol, List<EodDataModel> eodList);

}


class StockLocalDataSourceImpl implements StockLocalDataSource {

  final SharedPreferences sharedPreferences;
  StockLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<Unit> cachePopularStocks(List<StockModel> stocks) {

    final List<Map<String, dynamic>> jsonList = stocks
        .map((s) => {
              'symbol': s.symbol,
              'name': s.companyName,
            })
        .toList();

        final jsonString= json.encode(jsonList);


    sharedPreferences.setString(CACHED_STOCKS, jsonString);
    return Future.value(unit);
  }

  @override
  Future<List<StockModel>> getCachedPopularStocks() {
    final jsonString = sharedPreferences.getString(CACHED_STOCKS);
    if (jsonString != null) {
      final List decoded = json.decode(jsonString) as List;
      final models = decoded
          .map<StockModel>(
              (jsonItem) => StockModel.fromJson(jsonItem))
          .toList();
      return Future.value(models);
    } else {
      throw EmptyCacheException();
    }
  }

  @override
  Future<Unit>cacheEodData(String symbol, List<EodDataModel> eodList) {

    final key =CACHED_EOD_PREFIX+symbol;
    final List<Map<String, dynamic>> jsonList = eodList.map((e) {
      return {
        'symbol':e.symbol,
        'date': e.date.toIso8601String(),
        'open': e.open,
        'high': e.high,
        'low': e.low,
        'close': e.close,
        'volume': e.volume,
      };

    }).toList();
    final jsonString = json.encode(jsonList);

    sharedPreferences.setString(key,jsonString);
    return Future.value(unit);
  }

  @override
  Future<List<EodDataModel>> getCachedEodData(String symbol) {
    final key =CACHED_EOD_PREFIX+symbol;
    final jsonString = sharedPreferences.getString(key);
    if (jsonString != null) {
      final List decoded = json.decode(jsonString)as List;
      final models = decoded
          .map<EodDataModel>((jsonItem) =>
              EodDataModel.fromJson(jsonItem as Map<String, dynamic>))
          .toList();
      return Future.value(models);
    } else {
      throw EmptyCacheException();
    }
  }
}
