import 'package:equatable/equatable.dart';
import 'ticker_display.dart'; // استيراد كلاس TickerDisplay

abstract class TickerListState extends Equatable {
  const TickerListState();

  @override
  List<Object?> get props => [];
}

/// الحالة الابتدائية قبل التحميل
class TickerListInitial extends TickerListState {}

/// عند جلب البيانات بنجاح: يحمل قائمة TickerDisplay
class TickerListLoaded extends TickerListState {
  final List<TickerDisplay> tickers;

  const TickerListLoaded({required this.tickers});

  @override
  List<Object> get props => [tickers];
}

/// عند وجود خطأ: يحمل رسالة الخطأ
class TickerListError extends TickerListState {
  final String message;

  const TickerListError({required this.message});

  @override
  List<Object> get props => [message];
}

/// عند التحميل (تحميل أولي أو أثناء البحث)
class TickerListLoading extends TickerListState {}
