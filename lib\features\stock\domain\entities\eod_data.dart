import 'package:equatable/equatable.dart';

class EodData extends Equatable {
  final String symbol;
  final DateTime date;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;
  const EodData({
    required this.symbol,
    required this.date,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  @override
  List<Object?> get props => [symbol,date, open, high, low, close, volume];
}
