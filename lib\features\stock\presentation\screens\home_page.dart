import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/ticker_list/ticker_list_bloc.dart';
import '../bloc/ticker_list/ticker_list_event.dart';
import '../bloc/ticker_list/ticker_list_state.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/ticker_card_widget.dart';
import 'details_page.dart';


class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stock Tracker'),
        centerTitle: true,
        elevation: 2,
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SearchBarWidget(
              hintText: 'ابحث برمز السهم أو اسم الشركة...',
              onChanged: (query) {
                if (query.isEmpty) {
                  context.read<TickerListBloc>().add(LoadTickersEvent());
                } else {
                  context.read<TickerListBloc>().add(
                        SearchTickersEvent(query: query),
                      );
                }
              },
            ),
          ),

          // قائمة الأسهم مع دعم السحب للتحديث
          Expanded(
            child: BlocBuilder<TickerListBloc, TickerListState>(
              builder: (context, state) {
                if (state is TickerListLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is TickerListLoaded) {
                  final tickers = state.tickers;
                  if (tickers.isEmpty) {
                    return const Center(child: Text('لا توجد أسهم لعرضها.'));
                  }
                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<TickerListBloc>().add(LoadTickersEvent());
                      await Future.delayed(const Duration(seconds: 1));
                    },
                    child: ListView.separated(
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemCount: tickers.length,
                      separatorBuilder: (_, __) => const SizedBox.shrink(),
                      itemBuilder: (context, index) {
                        final ticker = tickers[index];
                        return TickerCardWidget(
                          ticker: ticker,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => StockDetailsPage(
                                  symbol: ticker.symbol,
                                  name: ticker.name,
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  );
                } else if (state is TickerListError) {
                  return Center(child: Text(state.message));
                } else {
                  // Initial state: لا يظهر شيء لأننا أضفنا التحميل في DI أو main.dart
                  return const SizedBox.shrink();
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
