import '../../domain/entities/stock.dart';


class StockModel extends Stock {

  const StockModel({
    required String symbol,
    required String companyName,
    
  }) : super(
          symbol: symbol,
          companyName: companyName,
          
        );
  factory StockModel.fromJson(Map<String, dynamic> json) {
    return StockModel(
      symbol: json['symbol'] as String,
      companyName: json['name'] as String,
      
    );
  }

}
