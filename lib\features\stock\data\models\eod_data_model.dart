import '../../domain/entities/eod_data.dart';


class EodDataModel extends EodData {
  const EodDataModel({
    required String symbol,
    required DateTime date,
    required double open,
    required double high,
    required double low,
    required double close,
    required double volume,
  }) : super(
          symbol: symbol,
          date: date,
          open: open,
          high: high,
          low: low,
          close: close,
          volume: volume,
        );
      static DateTime _parseDate(String dateString) {
    final cleaned = dateString.replaceFirst(RegExp(r'\+0000$'), 'Z');
    return DateTime.parse(cleaned);
  }
   factory EodDataModel.fromJson(Map<String, dynamic> json) {
    return EodDataModel(
      symbol: json['symbol'] as String,
      date: _parseDate(json['date'] as String),
      open: (json['open'] as num).toDouble(),
      high: (json['high'] as num).toDouble(),
      low: (json['low'] as num).toDouble(),
      close: (json['close'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
    );
  }


}
