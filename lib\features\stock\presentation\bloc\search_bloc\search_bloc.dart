
import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/error/failures.dart';
import '../../../domain/entities/stock.dart';
import '../../../domain/usecases/search_stocks.dart';
import 'search_event.dart';
import 'search_state.dart';



class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final SearchStocks searchStocks;

  SearchBloc(this.searchStocks) : super(SearchInitial()) {
    on<PerformSearchEvent>((event, emit) async {
      if (event.query.trim().isEmpty) {
        emit(SearchInitial());
        return;
      }

  
      emit(SearchLoading());
      final Either<Failure, List> result = await searchStocks(event.query);
      result.fold(
        (failure) => emit(SearchError(_mapFailureToMessage(failure))),
        (stocks) => emit(SearchLoaded(stocks as List<Stock>)),
      );
    });
  }


  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'خطأ في الخادم. حاول لاحقًا.';
      case NetworkFailure:
        return 'لا يوجد اتصال بالإنترنت.';
      default:
        return 'خطأ غير معروف.';
    }
  }
}
