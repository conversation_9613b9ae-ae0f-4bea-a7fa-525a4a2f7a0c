import 'package:internet_connection_checker/internet_connection_checker.dart';

/// واجهة مجردة للتحقق من حالة الاتصال بالإنترنت
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// التطبيق يعتمد على مكتبة InternetConnectionChecker للتحقق الفعلي
class NetworkInfoImpl implements NetworkInfo {
  final InternetConnectionChecker connectionChecker;

  NetworkInfoImpl(this.connectionChecker);

  @override
  Future<bool> get isConnected => connectionChecker.hasConnection;
}
