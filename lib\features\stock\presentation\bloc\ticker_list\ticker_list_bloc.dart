import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:stock_market/features/stock/domain/entities/eod_data.dart';
import 'package:stock_market/features/stock/domain/usecases/get_popular_stocks.dart';
import 'package:stock_market/features/stock/domain/usecases/search_stocks.dart';
import '../../../../../core/error/failures.dart';
import '../../../domain/entities/stock.dart';
import '../../../domain/usecases/get_eod_data.dart';
import 'ticker_list_event.dart';
import 'ticker_list_state.dart';
import 'ticker_display.dart'; // استيراد TickerDisplay


class TickerListBloc extends Bloc<TickerListEvent, TickerListState> {
  final GetPopularStocks getPopularStocks;
  final SearchStocks searchStocks;
  final GetEodData getEodData;

  TickerListBloc({
    required this.getPopularStocks,
    required this.searchStocks,
    required this.getEodData,
  }) : super(TickerListInitial()) {
    on<LoadTickersEvent>(_onLoadTickers);
    on<SearchTickersEvent>(_onSearchTickers);
  }

  Future<void> _onLoadTickers(
      LoadTickersEvent event, Emitter<TickerListState> emit) async {
    emit(TickerListLoading());

    // 1) استدعاء Use Case لجلب قائمة الأسهم (StockTicker)
    final Either<Failure, List<Stock>> tickersResult =
        await getPopularStocks();

    await tickersResult.fold(
      (failure) async {
        emit(TickerListError(message: _mapFailureToMessage(failure)));
      },
      (tickersList) async {
        // إذا نجحنا في جلب tickersList (List<StockTicker>),
        // نحتاج لبناء List<TickerDisplay> عن طريق جلب بيانات السعر لكل سهم.

        final List<TickerDisplay> displayList = [];

        // نعالج فقط أول 10 أسهم لضمان أداء مقبول (يمكن التعديل حسب الحاجة)
        final int limit = tickersList.length < 10 ? tickersList.length : 10;
        final List<Stock> limitedTickers = tickersList.sublist(0, limit);

        // نكرر على كل سهم للطلب و حساب السعر والنسبة
        for (final ticker in limitedTickers) {
          final String symbol = ticker.symbol;
          final String name = ticker.companyName;

          // 2) جلب أسعار آخر 5 أيام
          final Either<Failure, List<EodData>> pricesResult =
              await getEodData(symbol);

          await pricesResult.fold(
            (failure) {
              // في حال فشل جلب الأسعار، نعرض سهم مع بيانات صفرية (أو نتخلى عنه)
              displayList.add(
                TickerDisplay(
                  symbol: symbol,
                  name: name,
                  currentPrice: 0.0,
                  changePercent: 0.0,
                ),
              );
            },
            (pricesList) {
              if (pricesList.isNotEmpty) {
                // غالبًا يتم ترتيب pricesList من الأحدث أولاً (index 0 هو اليوم الأخير)
                final EodData latest = pricesList[0];
                double current = latest.close;

                // إذا كان هناك يوم سابق (index 1)، يمكننا حساب التغير
                double change = 0.0;
                if (pricesList.length > 1) {
                  final EodData previous = pricesList[1];
                  // نسبة التغير = (current - previous.close) / previous.close * 100
                  change = ((current - previous.close) / previous.close) * 100;
                }

                displayList.add(
                  TickerDisplay(
                    symbol: symbol,
                    name: name,
                    currentPrice: current,
                    changePercent: change,
                  ),
                );
              } else {
                // إذا لم توجد بيانات سعرية (نادراً)، نعرض صف بصفر
                displayList.add(
                  TickerDisplay(
                    symbol: symbol,
                    name: name,
                    currentPrice: 0.0,
                    changePercent: 0.0,
                  ),
                );
              }
            },
          );
        }

        // 3) نرتب القائمة حسب الترتيب الأصلي (اختياري)
        //    بناءً على ترتيب limitedTickers نفسه. displayList بالترتيب نفسه.

        emit(TickerListLoaded(tickers: displayList));
      },
    );
  }

  Future<void> _onSearchTickers(
      SearchTickersEvent event, Emitter<TickerListState> emit) async {
    emit(TickerListLoading());

    // 1) استدعاء Use Case للبحث بجملة البحث
    final Either<Failure, List<Stock>> searchResult =
        await searchStocks(event.query);

    await searchResult.fold(
      (failure) async {
        emit(TickerListError(message: _mapFailureToMessage(failure)));
      },
      (tickersList) async {
        final List<TickerDisplay> displayList = [];

        // ننفّذ على أول 10 نتائج (أو أقل إذا كانت القائمة أصغر)
        final int limit = tickersList.length < 10 ? tickersList.length : 10;
        final List<Stock> limitedTickers = tickersList.sublist(0, limit);

        for (final ticker in limitedTickers) {
          final String symbol = ticker.symbol;
          final String name = ticker.companyName;

          final Either<Failure, List<EodData>> pricesResult =
              await getEodData(symbol);

          await pricesResult.fold(
            (failure) {
              displayList.add(
                TickerDisplay(
                  symbol: symbol,
                  name: name,
                  currentPrice: 0.0,
                  changePercent: 0.0,
                ),
              );
            },
            (pricesList) {
              if (pricesList.isNotEmpty) {
                final EodData latest = pricesList[0];
                double current = latest.close;

                double change = 0.0;
                if (pricesList.length > 1) {
                  final EodData previous = pricesList[1];
                  change = ((current - previous.close) / previous.close) * 100;
                }

                displayList.add(
                  TickerDisplay(
                    symbol: symbol,
                    name: name,
                    currentPrice: current,
                    changePercent: change,
                  ),
                );
              } else {
                displayList.add(
                  TickerDisplay(
                    symbol: symbol,
                    name: name,
                    currentPrice: 0.0,
                    changePercent: 0.0,
                  ),
                );
              }
            },
          );
        }

        emit(TickerListLoaded(tickers: displayList));
      },
    );
  }

  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return 'خطأ في الخادم. حاول مرة أخرى لاحقًا.';
    } else if (failure is NetworkFailure) {
      return 'لا يوجد اتصال بالإنترنت.';
    } else if (failure is EmptyCacheFailure) {
      return 'لا توجد بيانات محفوظة في الكاش.';
    } else {
      return 'حدث خطأ غير متوقع.';
    }
  }
}
