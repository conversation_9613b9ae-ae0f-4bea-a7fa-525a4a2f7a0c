import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../datasources/stock_local_datasource.dart';
import '../datasources/stock_remote_datasource.dart';
import '../../domain/entities/stock.dart';
import '../../domain/entities/eod_data.dart';
import '../../domain/repositories/stock_repository.dart';

class StockRepositoryImpl implements StockRepository {
  final StockRemoteDataSource remoteDataSource;
  final StockLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  StockRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Stock>>> getPopularStocks() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteList = await remoteDataSource.fetchPopularStocks();
        await localDataSource.cachePopularStocks(remoteList);
        return Right(remoteList);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localList = await localDataSource.getCachedPopularStocks();
        return Right(localList);
      } on EmptyCacheException {
        return Left(EmptyCacheFailure());
      }
    }
  }
  @override
  Future<Either<Failure, List<EodData>>> getEodData(String symbol) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteList = await remoteDataSource.fetchEodData(symbol);
        await localDataSource.cacheEodData(symbol, remoteList);
        return Right(remoteList);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localList = await localDataSource.getCachedEodData(symbol);
        return Right(localList);
      } on EmptyCacheException {
        return Left(EmptyCacheFailure());
      }
    }
  }
  @override
  Future<Either<Failure,List<Stock>>> searchStocks(String query) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteList = await remoteDataSource.searchStock(query);
        return Right(remoteList);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localTickers = await localDataSource.getCachedPopularStocks();
        // نفلتر القائمة وفقًا للـ query (case-insensitive)
        final filtered = localTickers.where((ticker) {
          final symLower = ticker.symbol.toLowerCase();
          final nameLower = ticker.companyName.toLowerCase();
          final qLower = query.toLowerCase();
          return symLower.contains(qLower) || nameLower.contains(qLower);
        }).toList();
        return Right(filtered);
      } on EmptyCacheException {
        return Left(EmptyCacheFailure());
      }
    }
  }
}
