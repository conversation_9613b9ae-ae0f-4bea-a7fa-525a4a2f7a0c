import 'package:flutter/material.dart';
import '../../domain/entities/stock.dart';
import '../bloc/ticker_list/ticker_display.dart';


class StockListItem extends StatelessWidget {
  final TickerDisplay tickers;

  const StockListItem({Key? key, required this.tickers}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: ListTile(
        // العنوان الرئيسي: "SYMBOL - Company Name"
        title: Text('${tickers.symbol} - ${tickers.name}'),
        // السعر الحالي بالصيغة "$xxx.xx"
        // subtitle: Text(
        //   'السعر الحالي: \$${(stock.currentPrice ?? 0.0).toStringAsFixed(2)}',
        // ),
        // // نسبة التغير باللون المناسب
        // trailing: Text(
        //   '${change.toStringAsFixed(2)}%',
        //   style: TextStyle(color: changeColor),
        // ),
        onTap: () {
        //  Navigator.push(
        //     context,
        //     MaterialPageRoute(
        //       builder: (context) => DetailsPage(symbol: stock.symbol),
        //     ),
        //     );
        
        },
      ),
    );
  }
}
