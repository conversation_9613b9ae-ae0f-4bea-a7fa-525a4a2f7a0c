import 'package:equatable/equatable.dart';
import 'package:stock_market/features/stock/domain/entities/eod_data.dart';



abstract class EodState extends Equatable {
  @override
  List<Object?> get props => [];
}


class EodInitial extends EodState {}

class EodLoading extends EodState {}


class EodLoaded extends EodState {
  final List<EodData> eodList;

  EodLoaded({required this.eodList});

  @override
  List<Object?> get props => [eodList];
}

class EodError extends EodState {
  final String message;

  EodError(this.message);

  @override
  List<Object?> get props => [message];
}
