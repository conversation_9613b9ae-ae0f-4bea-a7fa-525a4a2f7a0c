import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:stock_market/features/stock/domain/entities/eod_data.dart';
import 'package:syncfusion_flutter_charts/charts.dart';


/// ويدجت تقوم برسم مخطط خطي لأسعار الإغلاق لآخر 5 أيام
/// تستقبل قائمة StockPrice مرتبة حسب الأحدث أولاً
class StockPriceChart extends StatelessWidget {
  final List<EodData> prices;

  const StockPriceChart({
    Key? key,
    required this.prices,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // لعكس الترتيب بحيث الأقدم أولاً حتى يظهر الرسم الزمني تصاعديًا
    final data = prices.reversed.toList();

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        // نعرض التاريخ فقط (مثال: "06-04")
        labelRotation: 45,
        labelFormatter: (datum, index) =>
            '${data[index].date.month.toString().padLeft(2, '0')}-${data[index].date.day.toString().padLeft(2, '0')}',
      ),
      primaryYAxis: NumericAxis(
        numberFormat: NumberFormat.currency(symbol: '\$', decimalDigits: 2),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <LineSeries<EodData, String>>[
        LineSeries<EodData, String>(
          dataSource: data,
          xValueMapper: (datum, index) =>
              '${data[index].date.month.toString().padLeft(2, '0')}-${data[index].date.day.toString().padLeft(2, '0')}',
          yValueMapper: (datum, index) => data[index].close,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
          enableTooltip: true,
        ),
      ],
    );
  }
}
