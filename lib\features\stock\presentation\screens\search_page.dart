import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/features/stock/presentation/bloc/ticker_list/ticker_display.dart';
import '../bloc/search_bloc/search_bloc.dart';
import '../bloc/search_bloc/search_state.dart';

import '../widgets/stock_list_item.dart';


class SearchPage extends StatelessWidget {
  final TickerDisplay tickerDisplay;
  const SearchPage({Key? key, required this.tickerDisplay}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بحث عن الأسهم'),
      ),
      body: Column(
        children: [
          const SearchBar(),
        
          Expanded(
            child: BlocBuilder<SearchBloc, SearchState>(
              builder: (context, state) {
                if (state is SearchInitial) {
                  return const Center(
                    child: Text('املأ حقل البحث لعرض النتائج'),
                  );
                } else if (state is SearchLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is SearchLoaded) {
                  final tickers = state.tickerDisplay;
                  if (stocks.isEmpty) {
                    return const Center(
                      child: Text('لا توجد نتائج مطابقة'),
                    );
                  }
                  return ListView.builder(
                    itemCount: stocks.length,
                    itemBuilder: (ctx, index) {
                      return StockListItem(tickers:  stocks[index]);
                    },
                  );
                } else if (state is SearchError) {
                  return Center(child: Text(state.message));
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
