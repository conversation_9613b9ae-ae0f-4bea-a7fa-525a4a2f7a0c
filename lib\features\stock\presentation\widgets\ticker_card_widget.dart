import 'package:flutter/material.dart';
import '../bloc/ticker_list/ticker_display.dart';

/// بطاقة تعرض رمز السهم واسم الشركة والسعر الحالي ونسبة التغير
class TickerCardWidget extends StatelessWidget {
  final TickerDisplay ticker; // تغير من StockTicker إلى TickerDisplay
  final VoidCallback onTap;

  const TickerCardWidget({
    Key? key,
    required this.ticker,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // نختار لون النص لنسبة التغير: أخضر إذا موجبة، أحمر إذا سالبة
    final bool isPositive = ticker.changePercent >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeString = (isPositive ? '+' : '') +
        ticker.changePercent.toStringAsFixed(2) +
        '%';

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: ListTile(
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          ticker.symbol,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              ticker.name,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  '\$${ticker.currentPrice.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  changeString,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: changeColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }
}