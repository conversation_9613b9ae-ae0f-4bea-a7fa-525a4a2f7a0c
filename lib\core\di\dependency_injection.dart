// lib/core/di/di.dart

import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../network/network_info.dart';
import '../../features/stock/data/datasources/stock_local_datasource.dart';
import '../../features/stock/data/datasources/stock_remote_datasource.dart';
import '../../features/stock/data/repositories/stock_repository_impl.dart';
import '../../features/stock/domain/usecases/get_popular_stocks.dart';
import '../../features/stock/domain/usecases/search_stocks.dart';
import '../../features/stock/domain/usecases/get_eod_data.dart';
import '../../features/stock/presentation/bloc/popular_bloc/popular_bloc.dart';
import '../../features/stock/presentation/bloc/search_bloc/search_bloc.dart';
import '../../features/stock/presentation/bloc/eod_bloc/eod_bloc.dart';
import '../../features/stock/presentation/bloc/theme_cubit/theme_cubit.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Core
  sl.registerLazySingleton<Dio>(() => Dio());
  sl.registerLazySingleton<Connectivity>(() => Connectivity());
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl<InternetConnectionChecker>()));

  // External / async
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton<SharedPreferences>(() => sharedPreferences);

  // Data sources
  sl.registerLazySingleton<StockLocalDataSource>(
    () => StockLocalDataSourceImpl(sharedPreferences: sl<SharedPreferences>()),
  );
  sl.registerLazySingleton<StockRemoteDataSource>(
    () => StockRemoteDataSourceImpl(dio: sl<Dio>()),
  );

  // Repository
  sl.registerLazySingleton<StockRepositoryImpl>(
    () => StockRepositoryImpl(
      remoteDataSource: sl<StockRemoteDataSource>(),
      localDataSource: sl<StockLocalDataSource>(),
      networkInfo: sl<NetworkInfo>(),
    ),
  );

  // Use Cases
  sl.registerLazySingleton<GetPopularStocks>(
    () => GetPopularStocks(sl<StockRepositoryImpl>()),
  );
  sl.registerLazySingleton<SearchStocks>(
    () => SearchStocks(sl<StockRepositoryImpl>()),
  );
  sl.registerLazySingleton<GetEodData>(
    () => GetEodData(sl<StockRepositoryImpl>()),
  );

  // Blocs / Cubits
  sl.registerLazySingleton<ThemeCubit>(() => ThemeCubit());
  sl.registerFactory<PopularBloc>(
    () => PopularBloc(sl<GetPopularStocks>()),
  );
  sl.registerFactory<SearchBloc>(
    () => SearchBloc(sl<SearchStocks>()),
  );
  sl.registerFactory<EodBloc>(
    () => EodBloc(sl<GetEodData>()),
  );
}
